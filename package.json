{"name": "gpace", "version": "1.0.0", "description": "GPAce - AI-powered task management and research assistant", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "vite build", "build:dev": "vite build --mode development", "preview": "vite preview", "serve:dev": "vite", "analyze": "vite build --mode analyze"}, "dependencies": {"@google/generative-ai": "^0.24.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "marked": "^11.1.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "bootstrap": "^5.3.2", "chart.js": "^4.4.0", "firebase": "^10.7.1", "quill": "^1.3.7", "three": "^0.157.0", "mathjax": "^3.2.2", "pdfjs-dist": "^3.11.174"}, "devDependencies": {"nodemon": "^3.0.2", "vite": "^5.0.0", "@vitejs/plugin-legacy": "^5.0.0", "vite-plugin-html": "^3.2.0", "rollup-plugin-copy": "^3.5.0", "terser": "^5.24.0"}}